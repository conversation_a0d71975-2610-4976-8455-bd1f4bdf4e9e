import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface SimpleBirthdayEmailProps {
  speakerName: string;
  subject?: string;
  body?: string;
  companyName?: string;
  stampMailbox?: string;
}

export const SimpleBirthdayEmail = ({
  speakerName = 'Speaker',
  subject = 'Happy Birthday! 🎉',
  body = `Dear {speaker_name},

Wishing you a very happy birthday! 🎂

Thank you for being an amazing part of our speaker community. Your contributions make a real difference, and we're grateful to have you with us.

Hope your special day is filled with joy, celebration, and all your favorite things!

Best wishes,
The Speaker Agency Team`,
  companyName = 'Speaker Agency',
  stampMailbox = '<EMAIL>',
}: SimpleBirthdayEmailProps) => {
  const previewText = `Happy Birthday, ${speakerName}! 🎉`;
  
  // Replace placeholder with actual speaker name
  const processedBody = body.replace(/{speaker_name}/g, speakerName);
  
  // Split body into paragraphs
  const paragraphs = processedBody.split('\n\n').filter(p => p.trim());

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={content}>
            <Heading style={h1}>🎉 {subject.replace('🎉', '').trim()} 🎉</Heading>
            
            {paragraphs.map((paragraph, index) => {
              // Skip signature lines for now
              if (paragraph.includes('Best wishes') || paragraph.includes('The Speaker Agency Team')) {
                return null;
              }
              
              return (
                <Text key={index} style={text}>
                  {paragraph}
                </Text>
              );
            })}

            {/* Footer */}
            <Section style={footer}>
              <Text style={footerText}>
                Best wishes,
              </Text>
              <Text style={footerSignature}>
                <strong>The {companyName} Team</strong>
              </Text>
              <Text style={footerContact}>
                {stampMailbox}
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default SimpleBirthdayEmail;

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
  borderRadius: '8px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
};

const content = {
  padding: '48px',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 32px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const footer = {
  textAlign: 'center' as const,
  marginTop: '40px',
  paddingTop: '32px',
  borderTop: '1px solid #e5e7eb',
};

const footerText = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
};

const footerSignature = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '8px 0',
};

const footerContact = {
  color: '#9ca3af',
  fontSize: '12px',
  margin: '8px 0',
};
